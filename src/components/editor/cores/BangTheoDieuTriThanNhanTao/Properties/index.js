import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row, Col, InputNumber, Checkbox, Input } from "antd";
import { Select } from "components";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { FontColorsOutlined } from "@ant-design/icons";
import {
  EditorTool,
  AlignConfig,
  FontSizeConfig,
} from "components/editor/config";
import { DS_LOAI_KY, VI_TRI_CA } from "../../ImageSign/constanst";
import { useListAll } from "hooks";
const { PickColor } = EditorTool;

const PhieuTheoDoiTNTProperties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const { apiFields } = props;
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  const [listAllKhoa] = useListAll("khoa");

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };

  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={24}>{t("editor.khoaChiDinhThuoc")}</Col>
        <Col span={24}>
          <Select
            data={listAllKhoa}
            onChange={onChangeValue("dsKhoaChiDinhThuocId")}
            mode="multiple"
            value={state.dsKhoaChiDinhThuocId}
          ></Select>
        </Col>
        <PropertiesSign
          state={state}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

PhieuTheoDoiTNTProperties.defaultProps = {
  state: {},
};

PhieuTheoDoiTNTProperties.propTypes = {
  state: T.shape({}),
};

export default PhieuTheoDoiTNTProperties;
