import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row, Col, Select, InputNumber, Checkbox, Input } from "antd";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";
import { FontColorsOutlined } from "@ant-design/icons";
import {
  EditorTool,
  AlignConfig,
  FontSizeConfig,
} from "components/editor/config";
import { DS_LOAI_KY, VI_TRI_CA } from "../../ImageSign/constanst";
import { PropertiesSign } from "../../ImageSign/Properties";
const { PickColor } = EditorTool;

const BangDanhGiaNguyCoTiDeProperties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  const onChangeInput = (key) => (e) => {
    setState({
      [key]: e.target.checked,
    });
  };
  return (
    <Main>
      <Row gutter={[12, 12]}>
        <PropertiesSign
          state={state}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

BangDanhGiaNguyCoTiDeProperties.defaultProps = {
  state: {},
};

BangDanhGiaNguyCoTiDeProperties.propTypes = {
  state: T.shape({}),
};

export default BangDanhGiaNguyCoTiDeProperties;
