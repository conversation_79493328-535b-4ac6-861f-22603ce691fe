import React, {
  useState,
  useEffect,
  useImperative<PERSON>andle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row } from "antd";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";

import { PropertiesSign } from "../../ImageSign/Properties";

const PhieuChamSocCap23CRProperties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const { apiFields } = props;
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };

  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <PropertiesSign
          state={state}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

PhieuChamSocCap23CRProperties.defaultProps = {
  state: {},
};

PhieuChamSocCap23CRProperties.propTypes = {
  state: T.shape({}),
};

export default PhieuChamSocCap23CRProperties;
