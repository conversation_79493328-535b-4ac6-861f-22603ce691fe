import React, {
  useState,
  useEffect,
  useImperativeHandle,
  forwardRef,
} from "react";
import T from "prop-types";
import { Row, Col, Checkbox, Input } from "antd";
import { Main } from "./styled";
import { useTranslation } from "react-i18next";

import { PropertiesSign } from "../../ImageSign/Properties";

const PhieuChamSocCap23Properties = forwardRef((props, ref) => {
  const { t } = useTranslation();
  const { apiFields } = props;
  const [state, _setState] = useState({});
  const setState = (data = {}) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    if (props.state.key) {
      let newState = {
        ...props.state.props,
      };
      setState(newState);
    }
  }, [props.state]);

  useImperativeHandle(ref, () => {
    return {
      ...state,
    };
  });

  const onChangeValue = (type) => (value) => {
    setState({
      [type]: value,
    });
  };

  const onChangeCheckbox = (type) => (e) => {
    onChangeValue(type)(e.target.checked);
  };

  const onChangeInput = (type) => (e) => {
    onChangeValue(type)(e.target.value);
    setState({
      [type]: e.target.value,
    });
  };

  const changeCheckbox = (target) => (e) => {
    setState({
      [target]: e.target.checked,
    });
  };

  return (
    <Main>
      <Row gutter={[12, 12]}>
        <Col span={8}>
          <span>{t("editor.hienHeaderForm")}</span>
        </Col>
        <Col span={16}>
          <Checkbox
            checked={state.showHeaderForm}
            onChange={changeCheckbox("showHeaderForm")}
          />
        </Col>
        <Col span={8}>{t("editor.tenPhieu")}</Col>
        <Col span={16}>
          <Input
            size={"small"}
            style={{ width: "100%" }}
            onChange={onChangeInput("titleForm")}
            value={state.titleForm}
          />
        </Col>
        <PropertiesSign
          state={state}
          changeCheckbox={changeCheckbox}
          onChangeInput={onChangeInput}
          onChangeValue={onChangeValue}
        ></PropertiesSign>
      </Row>
    </Main>
  );
});

PhieuChamSocCap23Properties.defaultProps = {
  state: {},
};

PhieuChamSocCap23Properties.propTypes = {
  state: T.shape({}),
};

export default PhieuChamSocCap23Properties;
