import React, {
  forwardRef,
  memo,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Select } from "antd";

import { isArray, openInNewTab } from "utils/index";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { Main } from "./styled";

const PhanLoaiNguoiBenh = ({ fromSetting, ...props }, ref) => {
  const { t } = useTranslation();
  const { disableTiepDon } = useContext(TiepDonContext);

  const dsPhanLoaiNbId = useSelector((state) => state.tiepDon.dsPhanLoaiNbId);

  const listAllPhanLoaiNB = useSelector(
    (state) => state.phanLoaiNB.listAllPhanLoaiNB
  );

  const {
    tiepDon: { updateData },
    phanLoaiNB: { getListAllPhanLoaiNB },
  } = useDispatch();

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListAllPhanLoaiNB(param);
  }, []);

  console.log("🚀 KhoaMilan -> listAllPhanLoaiNB", listAllPhanLoaiNB);

  return (
    <Main md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-select">
        <label
          onClick={() => openInNewTab("/danh-muc/phan-loai-nb")}
          className="label pointer"
        >
          {t("tiepDon.phanLoai")}
        </label>
        <Select
          className="select"
          placeholder={t("tiepDon.chonPhanLoai")}
          onChange={(e) => {
            console.log("🚀 KhoaMilan -> e", e);
            updateData({ dsPhanLoaiNbId: e });
          }}
          value={dsPhanLoaiNbId || []}
          disabled={disableTiepDon}
          mode="multiple"
          tagRender={(props) => {
            const { label, value, closable, onClose } = props;
            const item = listAllPhanLoaiNB.find((i) => i.id === value);
            const color = item?.mauChu || "#000";
            const bg = item?.mauNen || "#fff";

            return (
              <div
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  backgroundColor: bg,
                  color: color,
                  borderRadius: 4,
                  padding: "0 8px",
                  marginRight: 4,
                }}
              >
                {label}
                {closable && (
                  <span
                    onClick={onClose}
                    style={{ marginLeft: 4, cursor: "pointer" }}
                  >
                    ×
                  </span>
                )}
              </div>
            );
          }}
        >
          {isArray(listAllPhanLoaiNB, true) &&
            listAllPhanLoaiNB.map((item) => (
              <Select.Option key={item.id} value={item.id}>
                {item.ten}
              </Select.Option>
            ))}
        </Select>
      </div>
    </Main>
  );
};

export default memo(forwardRef(PhanLoaiNguoiBenh));
