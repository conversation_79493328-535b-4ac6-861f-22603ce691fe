import React, {
  forwardRef,
  memo,
  useContext,
  useEffect,
  useState,
} from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { Select } from "antd";

import { isArray, openInNewTab } from "utils/index";
import TiepDonContext from "pages/tiepDon/context/TiepDon";
import { Main } from "./styled";

const PhanLoaiNguoiBenh = ({ fromSetting, ...props }, ref) => {
  const { t } = useTranslation();
  const { disableTiepDon } = useContext(TiepDonContext);

  const dsPhanLoaiNbId = useSelector((state) => state.tiepDon.dsPhanLoaiNbId);

  const listAllPhanLoaiNB = useSelector(
    (state) => state.phanLoaiNB.listAllPhanLoaiNB
  );

  const {
    tiepDon: { updateData },
    phanLoaiNB: { getListAllPhanLoaiNB },
  } = useDispatch();

  useEffect(() => {
    let param = { active: true, page: "", size: "" };
    getListAllPhanLoaiNB(param);
  }, []);

  console.log("🚀 KhoaMilan -> listAllPhanLoaiNB", listAllPhanLoaiNB);

  return (
    <Main md={12} xl={12} xxl={12} {...props} ref={ref}>
      <div className="item-select">
        <label
          onClick={() => openInNewTab("/danh-muc/phan-loai-nb")}
          className="label pointer"
        >
          {t("tiepDon.phanLoai")}
        </label>
        <Select
          className="select"
          placeholder={t("tiepDon.chonPhanLoai")}
          onChange={(e) => {
            console.log("🚀 KhoaMilan -> e", e);
            updateData({ dsPhanLoaiNbId: e });
          }}
          value={dsPhanLoaiNbId || []}
          disabled={disableTiepDon}
          mode="multiple"
          tagRender={(props) => {
            const { label, value, closable, onClose } = props;
            const item = listAllPhanLoaiNB.find((i) => i.id === value);
            const color = item?.mauChu || "#000";
            const bg = item?.mauNen || "#f6f6f6";

            return (
              <span
                style={{
                  display: "inline-flex",
                  alignItems: "center",
                  backgroundColor: bg,
                  color: color,
                  border: "1px solid #d9d9d9",
                  borderRadius: "6px",
                  padding: "0 7px",
                  fontSize: "12px",
                  lineHeight: "20px",
                  marginRight: "4px",
                  marginBottom: "2px",
                  maxWidth: "99%",
                  position: "relative",
                  whiteSpace: "nowrap",
                  textOverflow: "ellipsis",
                  overflow: "hidden",
                  cursor: "default",
                  transition: "all 0.3s",
                }}
                onMouseEnter={(e) => {
                  e.target.style.opacity = "0.85";
                }}
                onMouseLeave={(e) => {
                  e.target.style.opacity = "1";
                }}
              >
                <span style={{ marginRight: closable ? "4px" : "0" }}>
                  {label}
                </span>
                {closable && (
                  <span
                    onClick={onClose}
                    style={{
                      cursor: "pointer",
                      fontSize: "10px",
                      color: "rgba(0, 0, 0, 0.45)",
                      fontWeight: "bold",
                      display: "inline-flex",
                      alignItems: "center",
                      justifyContent: "center",
                      width: "12px",
                      height: "12px",
                      borderRadius: "50%",
                      transition: "all 0.3s",
                    }}
                    onMouseEnter={(e) => {
                      e.target.style.backgroundColor = "rgba(0, 0, 0, 0.06)";
                      e.target.style.color = "rgba(0, 0, 0, 0.88)";
                    }}
                    onMouseLeave={(e) => {
                      e.target.style.backgroundColor = "transparent";
                      e.target.style.color = "rgba(0, 0, 0, 0.45)";
                    }}
                  >
                    ×
                  </span>
                )}
              </span>
            );
          }}
        >
          {isArray(listAllPhanLoaiNB, true) &&
            listAllPhanLoaiNB.map((item) => (
              <Select.Option key={item.id} value={item.id}>
                {item.ten}
              </Select.Option>
            ))}
        </Select>
      </div>
    </Main>
  );
};

export default memo(forwardRef(PhanLoaiNguoiBenh));
