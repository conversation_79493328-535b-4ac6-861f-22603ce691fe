export const initialLayout = {
  sectionOrder: ["dacDiemGiaiDoanSoRau", "tinhHinhSanPhuSauDe"],
  sections: {
    dacDiemGiaiDoanSoRau: {
      id: "dacDiemGiaiDoanSoRau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.dacDiemGiaiDoanSoRau",
      isCollapsible: true,
      fieldOrder: [
        "thoiGianRauSo",
        "canNangRau",
        "kieuRau",
        "matMang",
        "matMangKhac",
        "matMui",
        "matMuiKhac",
        "rauQuanCo",
        "chieuDaiRau",
        "thoiGianSo",
        "kiemSoatTuCung",
        "lyDoKiemSoatTuCung",
        "nguoiKiemSoatTuCungId",
        "cachSoRau",
        "bocRauNhanTao",
        "lyDoBocRauNhanTao",
        "chayMau",
        "soMauMat",
      ],
      fieldLayout: {
        thoiGianRauSo: { span: 6, offset: 0, active: true },
        canNangRau: { span: 10, offset: 0, active: true },
        kieuRau: { span: 8, offset: 0, active: true },
        matMang: { span: 6, offset: 0, active: true },
        matMangKhac: { span: 5, offset: 0, active: true },
        matMui: { span: 5, offset: 0, active: true },
        matMuiKhac: { span: 8, offset: 0, active: true },
        rauQuanCo: { span: 6, offset: 0, active: true },
        chieuDaiRau: { span: 10, offset: 0, active: true },
        thoiGianSo: { span: 8, offset: 0, active: true },
        kiemSoatTuCung: { span: 6, offset: 0, active: true },
        lyDoKiemSoatTuCung: { span: 10, offset: 0, active: true },
        nguoiKiemSoatTuCungId: { span: 8, offset: 0, active: true },
        cachSoRau: { span: 6, offset: 0, active: true },
        bocRauNhanTao: { span: 4, offset: 0, active: true },
        lyDoBocRauNhanTao: { span: 14, offset: 0, active: true },
        chayMau: { span: 6, offset: 0, active: true },
        soMauMat: { span: 4, offset: 0, active: true },
      },
    },
    tinhHinhSanPhuSauDe: {
      id: "tinhHinhSanPhuSauDe",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tinhHinhSanPhuSauDe",
      isCollapsible: true,
      fieldOrder: [
        "toanTrang",
        "daNiemMac",
        "mach",
        "huyetAp",
        "nhietDo",
        "nhipTho",
        "dsThongTinConSection",
        "lyDoCanThiep",
        "nguoiThucHienTsmId",
        "tangSinhMon",
        "tinhTrangTangSinhMon",
        "amDao",
        "tinhTrangAmDao",
        "coTuCung",
        "tinhTrangCoTuCung",
        "khauTangSinhMon",
        "soMuiKhauTangSinhMon",
        "loaiChiKhauTangSinhMon",
        "khauCoTuCung",
        "soMuiKhauCoTuCung",
        "loaiChiKhauCoTuCung",
        "canThiepKhac",
        "lyDoCanThiepKhac",
        "thoiGianChuyenDa",
        "thoiGianTheoDoiTaiKhoa",
        "nguoiMoId",
        "nguoiDo1Id",
        "nguoiDo2Id",
        "nguoiDo3Id",
      ],
      fieldLayout: {
        toanTrang: { span: 16, offset: 0, active: true },
        daNiemMac: { span: 8, offset: 0, active: true },
        mach: { span: 6, offset: 0, active: true },
        huyetAp: { span: 6, offset: 0, active: true },
        nhietDo: { span: 6, offset: 0, active: true },
        nhipTho: { span: 6, offset: 0, active: true },
        dsThongTinConSection: {
          span: 24,
          offset: 0,
          active: true,
          isLayoutSection: true,
          id: "dsThongTinConSection",
          i18n: "quanLyNoiTru.tongKetKhoaDe.thongTinCon",
          fieldOrder: ["thoiGianSinh", "hinhThucSinhId"],
          fieldLayout: {
            thoiGianSinh: { span: 6, offset: 0, active: true },
            hinhThucSinhId: { span: 8, offset: 0, active: true },
          },
        },
        lyDoCanThiep: { span: 8, offset: 0, active: true },
        nguoiThucHienTsmId: { span: 16, offset: 0, width: 12, active: true },
        tangSinhMon: { span: 8, offset: 0, active: true },
        tinhTrangTangSinhMon: { span: 16, offset: 0, active: true },
        amDao: { span: 8, offset: 0, active: true },
        tinhTrangAmDao: { span: 16, offset: 0, active: true },
        coTuCung: { span: 8, offset: 0, active: true },
        tinhTrangCoTuCung: { span: 16, offset: 0, active: true },
        khauTangSinhMon: { span: 8, offset: 0, active: true },
        soMuiKhauTangSinhMon: { span: 8, offset: 0, active: true },
        loaiChiKhauTangSinhMon: { span: 8, offset: 0, active: true },
        khauCoTuCung: { span: 8, offset: 0, active: true },
        soMuiKhauCoTuCung: { span: 8, offset: 0, active: true },
        loaiChiKhauCoTuCung: { span: 8, offset: 0, active: true },
        canThiepKhac: { span: 8, offset: 0, active: true },
        lyDoCanThiepKhac: { span: 8, offset: 0, active: true },
        thoiGianChuyenDa: { span: 8, offset: 0, active: true },
        thoiGianTheoDoiTaiKhoa: { span: 8, offset: 0, active: true },
        nguoiMoId: { span: 8, offset: 0, active: true },
        nguoiDo1Id: { span: 8, offset: 0, active: true },
        nguoiDo2Id: { span: 8, offset: 0, active: true },
        nguoiDo3Id: { span: 8, offset: 0, active: true },
      },
    },
  },
};

// Định nghĩa các field cơ bản cho tình trạng sản phụ sau đẻ
export const fieldDefinitions = {
  // Thông tin về rau
  thoiGianRauSo: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.rauSoLuc",
    type: "datetime",
    required: false,
    format: "DD/MM/YYYY HH:mm:ss",
    showTime: true,
  },
  canNangRau: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.canNangRauG",
    type: "number",
    required: false,
    min: 0,
    max: 9999,
    suffix: "g",
  },
  kieuRau: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.kieuSo",
    type: "checkbox-group",
    required: false,
  },
  rauQuanCo: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.dayRauCuonCo",
    type: "checkbox",
    required: false,
  },
  chieuDaiRau: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.chieuDaiDayRauCm",
    type: "number",
    required: false,
    min: 0,
    suffix: "cm",
  },
  thoiGianSo: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianRauSoPhut",
    type: "number",
    required: false,
    min: 0,
    suffix: "phút",
  },

  // Kiểm soát tử cung
  kiemSoatTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.kiemSoatTuCung",
    type: "checkbox-group",
    required: false,
  },
  lyDoKiemSoatTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.lyDo",
    type: "input",
    required: false,
  },
  nguoiKiemSoatTuCungId: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiThucHien",
    type: "select",
    required: false,
  },

  // Bóc rau nhân tạo
  bocRauNhanTao: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.bocRauNhanTao",
    type: "checkbox",
    required: false,
  },
  lyDoBocRauNhanTao: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.lyDo",
    type: "input",
    required: false,
  },

  // Chảy máu
  chayMau: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.chayMau",
    type: "checkbox",
    required: false,
  },
  soMauMat: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.soLuongMl",
    type: "number",
    required: false,
    min: 0,
    suffix: "ml",
  },

  // Tình hình sản phụ sau đẻ
  toanTrang: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.toanTrang",
    type: "input",
    required: false,
  },
  mach: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.machLanPhut",
    type: "number",
    required: false,
    min: 0,
    suffix: "lần/phút",
  },
  huyetAp: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.huyetApmmHg",
    required: false,
  },
  nhietDo: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nhietDoC",
    type: "number",
    required: false,
    min: 0,
    suffix: "°C",
  },
  nhipTho: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nhipTho",
    type: "number",
    required: false,
    min: 0,
    suffix: "lần/phút",
  },

  // Can thiệp
  lyDoCanThiep: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.lyDoCanThiep",
    type: "input",
    required: false,
  },
  tangSinhMon: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.tangSinhMon",
    type: "input",
    required: false,
  },
  nguoiThucHienTsmId: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiThucHienTSM",
    type: "select",
    required: false,
  },
  tinhTrangTangSinhMon: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangTangSinhMon",
    type: "checkbox-group",
    required: false,
  },

  // Âm đạo
  amDao: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.amDao",
    type: "input",
    required: false,
  },
  tinhTrangAmDao: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangAmDao",
    type: "checkbox-group",
    required: false,
  },

  // Cổ tử cung
  coTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.coTuCung",
    type: "input",
    required: false,
  },
  tinhTrangCoTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangCoTuCung",
    type: "checkbox-group",
    required: false,
  },

  // Can thiệp khác
  canThiepKhac: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.canThiepKhac",
    type: "input",
    required: false,
  },
  lyDoCanThiepKhac: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.lyDoCanThiepKhac",
    type: "input",
    required: false,
  },

  // Thời gian
  thoiGianChuyenDa: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianChuyenDa",
    type: "datetime",
    required: false,
    format: "DD/MM/YYYY HH:mm:ss",
    showTime: true,
  },
  thoiGianTheoDoiTaiKhoa: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianTheoDoiTaiKhoa",
    type: "datetime",
    required: false,
    format: "DD/MM/YYYY HH:mm:ss",
    showTime: true,
  },

  // Nhân viên
  nguoiMoId: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiMo",
    type: "select",
    required: false,
  },
  nguoiDo1Id: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDeLamThuThuat",
    type: "select",
    required: false,
  },
  nguoiDo2Id: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDePhuDoDe",
    type: "select",
    required: false,
  },
  nguoiDo3Id: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDeDonBe",
    type: "select",
    required: false,
  },

  // Màng và mũi
  matMang: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.matMang",
    type: "checkbox-group",
    required: false,
  },
  matMangKhac: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.matMangKhac",
    type: "input",
    required: false,
  },
  matMui: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.matMui",
    type: "checkbox-group",
    required: false,
  },
  matMuiKhac: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.matMuiKhac",
    type: "input",
    required: false,
  },

  // Da niêm mạc
  daNiemMac: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.daNiemMac",
    type: "checkbox-group",
    required: false,
  },

  // Khâu tầng sinh môn
  khauTangSinhMon: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.khauTangSinhMon",
    type: "checkbox-group",
    required: false,
  },
  soMuiKhauTangSinhMon: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.soMuiKhauTangSinhMon",
    type: "input",
    required: false,
    // min: 0,
  },
  loaiChiKhauTangSinhMon: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.loaiChiKhauTangSinhMon",
    type: "input",
    required: false,
  },

  // Khâu cổ tử cung
  khauCoTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.khauCoTuCung",
    type: "checkbox-group",
    required: false,
  },
  soMuiKhauCoTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.soMuiKhauCoTuCung",
    type: "input",
    required: false,
    // min: 0,
  },
  loaiChiKhauCoTuCung: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.loaiChiKhauCoTuCung",
    type: "input",
    required: false,
  },

  // Cách sổ rau
  cachSoRau: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.cachSoRau",
    type: "checkbox-group",
    required: false,
  },

  // Thông tin con
  thoiGianSinh: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.ngayGioDe",
    type: "datetime",
    required: false,
    format: "DD/MM/YYYY HH:mm:ss",
    showTime: true,
  },
  hinhThucSinhId: {
    i18n: "quanLyNoiTru.tongKetKhoaDe.cachDe",
    type: "select",
    required: false,
  },
};
