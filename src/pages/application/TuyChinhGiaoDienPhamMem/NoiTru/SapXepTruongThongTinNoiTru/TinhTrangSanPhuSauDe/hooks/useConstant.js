import { useMemo, useRef, useCallback } from "react";
import { useTranslation } from "react-i18next";
import { merge } from "lodash";
import { useEnum, useQueryAll } from "hooks";
import { safeConvertToArray } from "utils";
import { fieldDefinitions } from "../config";
import { query } from "redux-store/stores";
import { selectMaTen } from "redux-store/selectors";
import { ENUM, YES_NO } from "constants/index";

export const useConstant = ({ fromSetting = false }) => {
  const { t } = useTranslation();
  const listTinhTrangSanPhuSauDe = useRef([
    {
      id: "thoiGianRauSo",
      i18n: "quanLyNoiTru.tongKetKhoaDe.rauSoLuc",
      type: "datetime",
      required: false,
      format: "DD/MM/YYYY HH:mm:ss",
      showTime: true,
    },
    {
      id: "canNangRau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.canNangRauG",
      type: "numberFormat",
      required: false,
      suffix: "g",
      inputNumberFormatProps: {
        decimalScale: 0,
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0 && (floatValue || 0) < 10000;
        },
      },
    },
    {
      id: "kieuRau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.kieuSo",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "rauQuanCo",
      i18n: "quanLyNoiTru.tongKetKhoaDe.dayRauCuonCo",
      type: "checkbox",
      required: false,
    },
    {
      id: "chieuDaiRau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.chieuDaiDayRauCm",
      type: "numberFormat",
      required: false,
      suffix: "cm",
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "thoiGianSo",
      i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianRauSoPhut",
      type: "numberFormat",
      required: false,
      suffix: "phút",
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "kiemSoatTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.kiemSoatTuCung",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "lyDoKiemSoatTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.lyDo",
      type: "textField",
      required: false,
    },
    {
      id: "nguoiKiemSoatTuCungId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiThucHien",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "bocRauNhanTao",
      i18n: "quanLyNoiTru.tongKetKhoaDe.bocRauNhanTao",
      type: "checkbox",
      required: false,
    },
    {
      id: "lyDoBocRauNhanTao",
      i18n: "quanLyNoiTru.tongKetKhoaDe.lyDo",
      type: "textField",
      required: false,
    },
    {
      id: "chayMau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.chayMau",
      type: "checkbox",
      required: false,
    },
    {
      id: "soMauMat",
      i18n: "quanLyNoiTru.tongKetKhoaDe.soLuongMl",
      type: "numberFormat",
      required: false,
      suffix: "ml",
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "toanTrang",
      i18n: "quanLyNoiTru.tongKetKhoaDe.toanTrang",
      type: "textField",
      required: false,
    },
    {
      id: "mach",
      i18n: "quanLyNoiTru.tongKetKhoaDe.machLanPhut",
      type: "numberFormat",
      required: false,
      suffix: t("common.danhSachDonVi.nhipMach"),
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "huyetAp",
      i18n: "quanLyNoiTru.tongKetKhoaDe.huyetApmmHg",
      required: false,
    },
    {
      id: "nhietDo",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nhietDoC",
      type: "numberFormat",
      required: false,
      suffix: "°C",
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "nhipTho",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nhipTho",
      type: "numberFormat",
      required: false,
      inputNumberFormatProps: {
        timeDelay: 0,
        isAllowed: (values) => {
          const { floatValue } = values;
          return (floatValue || 0) >= 0;
        },
      },
    },
    {
      id: "lyDoCanThiep",
      i18n: "quanLyNoiTru.tongKetKhoaDe.lyDoCanThiep",
      type: "textField",
      required: false,
    },
    {
      id: "tangSinhMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tangSinhMon",
      type: "textField",
      required: false,
    },
    {
      id: "nguoiThucHienTsmId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiThucHienTSM",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "tinhTrangTangSinhMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangTangSinhMon",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "amDao",
      i18n: "quanLyNoiTru.tongKetKhoaDe.amDao",
      type: "textField",
      required: false,
    },
    {
      id: "tinhTrangAmDao",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangAmDao",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "coTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.coTuCung",
      type: "textField",
      required: false,
    },
    {
      id: "tinhTrangCoTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.tinhTrangCoTuCung",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "canThiepKhac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.canThiepKhac",
      type: "textField",
      required: false,
    },
    {
      id: "lyDoCanThiepKhac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.lyDoCanThiepKhac",
      type: "textField",
      required: false,
    },
    {
      id: "thoiGianChuyenDa",
      i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianChuyenDa",
      type: "datetime",
      required: false,
      format: "DD/MM/YYYY HH:mm:ss",
      showTime: true,
    },
    {
      id: "thoiGianTheoDoiTaiKhoa",
      i18n: "quanLyNoiTru.tongKetKhoaDe.thoiGianTheoDoiTaiKhoa",
      type: "datetime",
      required: false,
      format: "DD/MM/YYYY HH:mm:ss",
      showTime: true,
    },
    {
      id: "nguoiMoId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiMo",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "nguoiDo1Id",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDeLamThuThuat",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "nguoiDo2Id",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDePhuDoDe",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "nguoiDo3Id",
      i18n: "quanLyNoiTru.tongKetKhoaDe.nguoiDoDeDonBe",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
    {
      id: "matMang",
      i18n: "quanLyNoiTru.tongKetKhoaDe.matMang",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "matMangKhac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.matMangKhac",
      type: "textField",
      required: false,
    },
    {
      id: "matMui",
      i18n: "quanLyNoiTru.tongKetKhoaDe.matMui",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "matMuiKhac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.matMuiKhac",
      type: "textField",
      required: false,
    },
    {
      id: "daNiemMac",
      i18n: "quanLyNoiTru.tongKetKhoaDe.daNiemMac",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "khauTangSinhMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.khauTangSinhMon",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "soMuiKhauTangSinhMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.soMuiKhauTangSinhMon",
      type: "textField",
      required: false,
      // inputNumberFormatProps: {
      //   timeDelay: 0,
      //   isAllowed: (values) => {
      //     const { floatValue } = values;
      //     return (floatValue || 0) >= 0;
      //   },
      // },
    },
    {
      id: "loaiChiKhauTangSinhMon",
      i18n: "quanLyNoiTru.tongKetKhoaDe.loaiChiKhauTangSinhMon",
      type: "textField",
      required: false,
    },
    {
      id: "khauCoTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.khauCoTuCung",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "soMuiKhauCoTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.soMuiKhauCoTuCung",
      type: "textField",
      required: false,
      // inputNumberFormatProps: {
      //   timeDelay: 0,
      //   isAllowed: (values) => {
      //     const { floatValue } = values;
      //     return (floatValue || 0) >= 0;
      //   },
      // },
    },
    {
      id: "loaiChiKhauCoTuCung",
      i18n: "quanLyNoiTru.tongKetKhoaDe.loaiChiKhauCoTuCung",
      type: "textField",
      required: false,
    },
    {
      id: "cachSoRau",
      i18n: "quanLyNoiTru.tongKetKhoaDe.cachSoRau",
      type: "checkboxGroupOne",
      required: false,
    },
    {
      id: "dsThongTinConSection",
      i18n: "quanLyNoiTru.tongKetKhoaDe.thongTinCon",
      type: "section",
      required: false,
    },
    // Thông tin con
    {
      id: "thoiGianSinh",
      i18n: "quanLyNoiTru.tongKetKhoaDe.ngayGioDe",
      type: "datetime",
      required: false,
      format: "DD/MM/YYYY HH:mm:ss",
      showTime: true,
    },
    {
      id: "hinhThucSinhId",
      i18n: "quanLyNoiTru.tongKetKhoaDe.cachDe",
      type: "select",
      required: false,
      getLabel: selectMaTen,
      dropdownMatchSelectWidth: 300,
    },
  ]).current;

  const { data: listAllNhanVien } = useQueryAll(
    query.nhanVien.queryAllNhanVien
  );
  const { data: listAllCachThucDe } = useQueryAll(
    query.cachThucDe.queryAllCachThucDe
  );

  // Enum data
  const [listKieuRau] = useEnum(ENUM.KIEU_RAU);
  const [listTinhTrangTangSinhMon] = useEnum(ENUM.TINH_TRANG_TANG_SINH_MON);
  const [listMatMang] = useEnum(ENUM.MAT_MANG);
  const [listKiemSoatTuCung] = useEnum(ENUM.KIEM_SOAT_TU_CUNG);
  const [listDaNiemMac] = useEnum(ENUM.DA_NIEM_MAC);
  const [listLoaiKhau] = useEnum(ENUM.LOAI_KHAU);
  const [listCachSoRau] = useEnum(ENUM.CACH_SO_RAU);

  const fieldEnhancementConfig = useMemo(
    () => ({
      // Fields with list selects
      listSelectFields: {
        nguoiKiemSoatTuCungId: listAllNhanVien,
        nguoiThucHienTsmId: listAllNhanVien,
        nguoiMoId: listAllNhanVien,
        nguoiDo1Id: listAllNhanVien,
        nguoiDo2Id: listAllNhanVien,
        nguoiDo3Id: listAllNhanVien,
        hinhThucSinhId: listAllCachThucDe,
        kieuRau: listKieuRau,
        kiemSoatTuCung: listKiemSoatTuCung,
        tinhTrangTangSinhMon: listTinhTrangTangSinhMon,
        tinhTrangAmDao: listTinhTrangTangSinhMon,
        tinhTrangCoTuCung: listTinhTrangTangSinhMon,
        matMang: listMatMang,
        matMui: listMatMang,
        daNiemMac: listDaNiemMac,
        khauTangSinhMon: listLoaiKhau,
        khauCoTuCung: listLoaiKhau,
        cachSoRau: listCachSoRau,
      },
      specificFields: {},
      conditionalRequiredFields: {
        basicRequired: new Set([
          // Có thể thêm các field bắt buộc nếu cần
        ]),
      },
    }),
    [
      listAllNhanVien,
      listAllCachThucDe,
      listKieuRau,
      listKiemSoatTuCung,
      listTinhTrangTangSinhMon,
      listMatMang,
      listDaNiemMac,
      listLoaiKhau,
      listCachSoRau,
      t,
      fromSetting,
    ]
  );

  const fieldEnhancers = useMemo(
    () => ({
      addListSelect: (field, fieldId, config) => {
        const listSelect = config.listSelectFields[fieldId];
        return listSelect ? merge({}, field, { listSelect }) : field;
      },

      addSpecificProperties: (field, fieldId, config) => {
        const specificProps = config.specificFields[fieldId];
        return specificProps ? merge({}, field, specificProps) : field;
      },

      addConditionalRequired: (field, fieldId, config) => {
        const { basicRequired } = config.conditionalRequiredFields;

        if (!basicRequired.has(fieldId)) {
          return field;
        }

        const conditions = [basicRequired.has(fieldId)];

        const isRequired = conditions.some((condition) => condition);

        return isRequired ? merge({}, field, { required: true }) : field;
      },
    }),
    []
  );

  const allFieldDefinitions = useMemo(() => {
    const map = new Map();
    const allLists = [listTinhTrangSanPhuSauDe];

    allLists.forEach((list) => {
      safeConvertToArray(list).forEach((field) => {
        if (field?.id) {
          if (map.has(field.id)) {
            console.warn(`Duplicate field ID found: ${field.id}. Overwriting.`);
          }

          const enhancedField = [
            fieldEnhancers.addListSelect,
            fieldEnhancers.addSpecificProperties,
            fieldEnhancers.addConditionalRequired,
          ].reduce(
            (currentField, enhancer) =>
              enhancer(currentField, field.id, fieldEnhancementConfig),
            { ...field }
          );

          map.set(field.id, enhancedField);
        }
      });
    });

    return map;
  }, [listTinhTrangSanPhuSauDe, fieldEnhancementConfig, fieldEnhancers]);

  const getFieldDefinitionById = useCallback(
    (fieldId) => {
      return (
        allFieldDefinitions.get(fieldId) || fieldDefinitions[fieldId] || null
      );
    },
    [allFieldDefinitions]
  );

  return {
    // Field definitions
    listTinhTrangSanPhuSauDe,
    getFieldDefinitionById,
    allFieldDefinitions,

    // Enum data
    listAllNhanVien,
    listAllCachThucDe,
    listKieuRau,
    listTinhTrangTangSinhMon,
    listMatMang,
    listKiemSoatTuCung,
    listDaNiemMac,
    listLoaiKhau,
    listCachSoRau,
    // Constants
    YES_NO,
  };
};
